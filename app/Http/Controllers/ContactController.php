<?php

namespace App\Http\Controllers;

use App\Mail\PropertyInquiry; // Added for Lokus MVP
use App\Models\Property; // Added for Lokus MVP
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail; // Added for Lokus MVP

class ContactController extends Controller
{
    /**
     * Send property inquiry email to the lister.
     */
    public function send(Request $request, Property $property)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string|min:10|max:1000',
        ]);

        // Send email to property owner
        Mail::to($property->user->email)->send(new PropertyInquiry(
            $property,
            $request->name,
            $request->email,
            $request->phone,
            $request->message
        ));

        return back()->with('success', 'Your inquiry has been sent successfully!');
    }
}
