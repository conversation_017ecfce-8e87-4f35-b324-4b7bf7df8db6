<?php

namespace App\Livewire;

use App\Models\Property;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CreateProperty extends Component
{
    use WithFileUploads;

    public $property_type = '';
    public $listing_type = '';
    public $title = '';
    public $description = '';
    public $price = '';
    public $address_line_1 = '';
    public $city = '';
    public $state_region = '';
    public $zip_code = '';
    public $bedrooms = '';
    public $bathrooms = '';
    public $square_footage = '';
    public $plot_size = '';
    public $latitude = '';
    public $longitude = '';
    public $images = []; // For file uploads
    public $status = 'published'; // Default status as per PRD

    protected function rules()
    {
        return [
            'property_type' => ['required', 'string', Rule::in(['apartment', 'house', 'land', 'single_room'])],
            'listing_type' => ['required', 'string', Rule::in(['for_sale', 'for_rent'])],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'price' => ['required', 'numeric', 'min:0'],
            'address_line_1' => ['required', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'state_region' => ['nullable', 'string', 'max:255'],
            'zip_code' => ['nullable', 'string', 'max:255'],
            'bedrooms' => ['nullable', 'integer', 'min:0'],
            'bathrooms' => ['nullable', 'integer', 'min:0'],
            'square_footage' => ['nullable', 'numeric', 'min:0'],
            'plot_size' => ['nullable', 'numeric', 'min:0'],
            'latitude' => ['nullable', 'numeric', 'between:-90,90'], // Standard latitude range
            'longitude' => ['nullable', 'numeric', 'between:-180,180'], // Standard longitude range
            'images.*' => ['nullable', 'image', 'max:10240'], // 10MB Max per image
            'images' => ['max:15'], // Max 15 images
            'status' => ['required', 'string', Rule::in(['draft', 'published'])],
        ];
    }

    public function updatedPropertyType()
    {
        // Reset feature fields when property type changes
        $this->reset(['bedrooms', 'bathrooms', 'square_footage', 'plot_size']);
    }

    public function store()
    {
        $this->validate();

        $features = [];
        if (in_array($this->property_type, ['apartment', 'house', 'single_room'])) {
            $features['bedrooms'] = $this->bedrooms;
            $features['bathrooms'] = $this->bathrooms;
            $features['square_footage'] = $this->square_footage;
        } elseif ($this->property_type === 'land') {
            $features['plot_size'] = $this->plot_size;
        }

        $imagePaths = [];
        foreach ($this->images as $image) {
            $path = $image->store('properties', 'public'); // Store images in storage/app/public/properties
            $imagePaths[] = $path; // Store relative path only (e.g., 'properties/filename.jpg')
        }

        Property::create([
            'user_id' => Auth::id(),
            'property_type' => $this->property_type,
            'listing_type' => $this->listing_type,
            'title' => $this->title,
            'description' => $this->description,
            'price' => $this->price,
            'address_line_1' => $this->address_line_1,
            'city' => $this->city,
            'state_region' => $this->state_region,
            'zip_code' => $this->zip_code,
            'features' => $features,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'images' => $imagePaths,
            'status' => $this->status,
        ]);

        session()->flash('message', 'Property successfully created.');

        $this->redirect(route('lister.properties.index'), navigate: true);
    }

    public function render()
    {
        return view('livewire.create-property');
    }
}
