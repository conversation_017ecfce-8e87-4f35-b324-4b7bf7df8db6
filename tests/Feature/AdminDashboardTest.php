<?php

use App\Models\User;
use App\Models\Property;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('admin can access admin dashboard', function () {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
    ]);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);
    $response->assertSee('Admin Dashboard');
    $response->assertSee('Total Users');
    $response->assertSee('Total Properties');
});

test('non-admin users cannot access admin dashboard', function () {
    $user = User::factory()->create([
        'role' => 'seeker',
        'is_active' => true,
    ]);

    $this->actingAs($user);

    $response = $this->get('/admin');
    $response->assertStatus(403);
});

test('admin dashboard shows correct statistics', function () {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
    ]);

    // Create test data
    User::factory()->count(5)->create(['role' => 'lister']);
    User::factory()->count(10)->create(['role' => 'seeker']);
    
    $lister = User::factory()->create(['role' => 'lister']);
    Property::factory()->count(3)->create([
        'user_id' => $lister->id,
        'status' => 'published'
    ]);
    Property::factory()->count(2)->create([
        'user_id' => $lister->id,
        'status' => 'draft'
    ]);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);
    
    // Check if statistics are displayed
    $response->assertSee('16'); // Total users (admin + 5 listers + 10 seekers)
    $response->assertSee('5'); // Total properties
    $response->assertSee('3'); // Published properties
    $response->assertSee('2'); // Pending properties
});

test('admin dashboard shows user role breakdown', function () {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
    ]);

    // Create users with different roles
    User::factory()->count(2)->create(['role' => 'admin']);
    User::factory()->count(3)->create(['role' => 'lister']);
    User::factory()->count(5)->create(['role' => 'seeker']);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);
    
    // Check role breakdown
    $response->assertSee('3'); // Admin users (including the test admin)
    $response->assertSee('3'); // Lister users
    $response->assertSee('5'); // Seeker users
});

test('admin dashboard shows recent activity', function () {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
    ]);

    $lister = User::factory()->create([
        'role' => 'lister',
        'name' => 'Test Lister'
    ]);

    $property = Property::factory()->create([
        'user_id' => $lister->id,
        'title' => 'Test Property',
        'status' => 'published'
    ]);

    $this->actingAs($admin);

    $response = $this->get('/admin');
    $response->assertStatus(200);
    
    // Check if recent activity is shown
    $response->assertSee('Recent Properties');
    $response->assertSee('Test Property');
    $response->assertSee('Test Lister');
});
