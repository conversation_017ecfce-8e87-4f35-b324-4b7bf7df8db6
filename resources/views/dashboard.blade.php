<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @auth
                        @if(Auth::user()->role === 'lister')
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __("Lister Dashboard") }}</h3>
                            <p class="mb-2">{{ __("Welcome, Lister! Here are some quick actions:") }}</p>
                            <div class="flex space-x-4">
                                <a href="{{ route('lister.properties.create') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    {{ __("Create New Property") }}
                                </a>
                                <a href="{{ route('lister.properties.index') }}" class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150">
                                    {{ __("View My Properties") }}
                                </a>
                            </div>
                        @elseif(Auth::user()->role === 'seeker')
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __("Seeker Dashboard") }}</h3>
                            <p class="mb-2">{{ __("Welcome, Seeker! Start exploring properties:") }}</p>
                            <a href="{{ route('properties.index') }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 focus:bg-gray-700 active:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                {{ __("Search Properties") }}
                            </a>
                        @else
                            <p>{{ __("You're logged in!") }}</p>
                        @endif
                    @endauth
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
