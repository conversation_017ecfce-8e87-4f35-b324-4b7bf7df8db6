<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Lokus') }}</title>

        <!-- Fonts -->
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

        <!-- Scripts -->
        @livewireStyles
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
        @vite(['resources/css/app.css', 'resources/js/app.js'])

    </head>
    <body class="bg-gray-50" x-data="{ 
        currentPage: '{{ request()->path() }}', 
        showMobileMenu: false,
        showLoginModal: false,
        showContactModal: false,
        
        isActive(path) {
            return this.currentPage === path || this.currentPage.startsWith(path + '/');
        }
    }">

        <!-- Standardized Public Header -->
        <nav class="bg-white shadow-lg sticky top-0 z-50" role="navigation" aria-label="Main navigation">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <!-- Logo/Brand -->
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="flex-shrink-0 flex items-center" wire:navigate>
                            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                Lokus
                            </h1>
                        </a>
                    </div>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:flex items-center space-x-6">
                        <a href="{{ route('home') }}"
                           class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->is('/') ? 'text-blue-600 font-semibold' : '' }}"
                           wire:navigate>
                            Home
                        </a>
                        <a href="{{ route('properties.index') }}"
                           class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->routeIs('properties.index') ? 'text-blue-600 font-semibold' : '' }}"
                           wire:navigate>
                            Search Properties
                        </a>

                        @auth
                            @if (auth()->user()->role === 'lister')
                                <a href="{{ route('lister.properties.index') }}"
                                   class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->routeIs('lister.properties.*') ? 'text-blue-600 font-semibold' : '' }}"
                                   wire:navigate>
                                    My Listings
                                </a>
                                <a href="{{ route('lister.properties.create') }}"
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                                   wire:navigate>
                                    <i class="fas fa-plus mr-1"></i>
                                    Create Listing
                                </a>
                            @endif

                            @if (auth()->user()->role === 'admin')
                                <a href="{{ route('admin.dashboard') }}"
                                   class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200 {{ request()->routeIs('admin.*') ? 'text-blue-600 font-semibold' : '' }}"
                                   wire:navigate>
                                    <i class="fas fa-shield-alt mr-1"></i>
                                    Admin
                                </a>
                            @endif

                            <!-- User Dropdown -->
                            <div class="relative" x-data="{ userMenuOpen: false }">
                                <button @click="userMenuOpen = !userMenuOpen"
                                        class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
                                        aria-expanded="false" aria-haspopup="true">
                                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-2">
                                        {{ substr(auth()->user()->name, 0, 1) }}
                                    </div>
                                    {{ auth()->user()->name }}
                                    <i class="fas fa-chevron-down ml-1 text-xs"></i>
                                </button>

                                <div x-show="userMenuOpen"
                                     @click.away="userMenuOpen = false"
                                     x-transition:enter="transition ease-out duration-100"
                                     x-transition:enter-start="transform opacity-0 scale-95"
                                     x-transition:enter-end="transform opacity-100 scale-100"
                                     x-transition:leave="transition ease-in duration-75"
                                     x-transition:leave-start="transform opacity-100 scale-100"
                                     x-transition:leave-end="transform opacity-0 scale-95"
                                     class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                                    <a href="{{ route('profile.edit') }}"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                       wire:navigate>
                                        <i class="fas fa-user mr-2"></i>
                                        Profile
                                    </a>
                                    <a href="{{ route('settings.profile') }}"
                                       class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                       wire:navigate>
                                        <i class="fas fa-cog mr-2"></i>
                                        Settings
                                    </a>
                                    <hr class="my-1">
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                            <i class="fas fa-sign-out-alt mr-2"></i>
                                            Logout
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @else
                            <button @click="showLoginModal = true"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                                <i class="fas fa-sign-in-alt mr-1"></i>
                                Sign In
                            </button>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}"
                                   class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors duration-200"
                                   wire:navigate>
                                    Register
                                </a>
                            @endif
                        @endauth
                    </div>

                    <!-- Mobile Menu Button -->
                    <div class="md:hidden flex items-center">
                        <button @click="showMobileMenu = !showMobileMenu"
                                class="text-gray-700 hover:text-blue-600 p-2 rounded-lg transition-colors duration-200"
                                aria-label="Toggle mobile menu"
                                :aria-expanded="showMobileMenu">
                            <i class="fas fa-bars text-xl" x-show="!showMobileMenu"></i>
                            <i class="fas fa-times text-xl" x-show="showMobileMenu" x-cloak></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile Navigation Menu -->
            <div x-show="showMobileMenu"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform -translate-y-2"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform translate-y-0"
                 x-transition:leave-end="opacity-0 transform -translate-y-2"
                 x-cloak
                 class="md:hidden bg-white border-t border-gray-200 shadow-lg">
                <div class="px-4 pt-4 pb-6 space-y-2">
                    <a href="{{ route('home') }}"
                       @click="showMobileMenu = false"
                       class="block px-3 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 {{ request()->is('/') ? 'text-blue-600 bg-blue-50' : '' }}"
                       wire:navigate>
                        <i class="fas fa-home mr-3"></i>
                        Home
                    </a>
                    <a href="{{ route('properties.index') }}"
                       @click="showMobileMenu = false"
                       class="block px-3 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 {{ request()->routeIs('properties.index') ? 'text-blue-600 bg-blue-50' : '' }}"
                       wire:navigate>
                        <i class="fas fa-search mr-3"></i>
                        Search Properties
                    </a>

                    @auth
                        @if (auth()->user()->role === 'lister')
                            <a href="{{ route('lister.properties.index') }}"
                               @click="showMobileMenu = false"
                               class="block px-3 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 {{ request()->routeIs('lister.properties.*') ? 'text-blue-600 bg-blue-50' : '' }}"
                               wire:navigate>
                                <i class="fas fa-building mr-3"></i>
                                My Listings
                            </a>
                            <a href="{{ route('lister.properties.create') }}"
                               @click="showMobileMenu = false"
                               class="block px-3 py-3 text-base font-medium bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors duration-200"
                               wire:navigate>
                                <i class="fas fa-plus mr-3"></i>
                                Create Listing
                            </a>
                        @endif

                        @if (auth()->user()->role === 'admin')
                            <a href="{{ route('admin.dashboard') }}"
                               @click="showMobileMenu = false"
                               class="block px-3 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 {{ request()->routeIs('admin.*') ? 'text-blue-600 bg-blue-50' : '' }}"
                               wire:navigate>
                                <i class="fas fa-shield-alt mr-3"></i>
                                Admin Dashboard
                            </a>
                        @endif

                        <!-- Mobile User Section -->
                        <div class="border-t border-gray-200 pt-4 mt-4">
                            <div class="flex items-center px-3 py-2 mb-3">
                                <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium mr-3">
                                    {{ substr(auth()->user()->name, 0, 1) }}
                                </div>
                                <div>
                                    <div class="text-base font-medium text-gray-900">{{ auth()->user()->name }}</div>
                                    <div class="text-sm text-gray-500">{{ auth()->user()->email }}</div>
                                </div>
                            </div>
                            <a href="{{ route('profile.edit') }}"
                               @click="showMobileMenu = false"
                               class="block px-3 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-200"
                               wire:navigate>
                                <i class="fas fa-user mr-3"></i>
                                Profile
                            </a>
                            <a href="{{ route('settings.profile') }}"
                               @click="showMobileMenu = false"
                               class="block px-3 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-200"
                               wire:navigate>
                                <i class="fas fa-cog mr-3"></i>
                                Settings
                            </a>
                            <form method="POST" action="{{ route('logout') }}" class="mt-2">
                                @csrf
                                <button type="submit"
                                        class="block w-full text-left px-3 py-3 text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200">
                                    <i class="fas fa-sign-out-alt mr-3"></i>
                                    Logout
                                </button>
                            </form>
                        </div>
                    @else
                        <button @click="showLoginModal = true; showMobileMenu = false"
                                class="block w-full px-3 py-3 text-base font-medium bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors duration-200">
                            <i class="fas fa-sign-in-alt mr-3"></i>
                            Sign In
                        </button>
                        @if (Route::has('register'))
                            <a href="{{ route('register') }}"
                               @click="showMobileMenu = false"
                               class="block px-3 py-3 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-lg transition-colors duration-200"
                               wire:navigate>
                                <i class="fas fa-user-plus mr-3"></i>
                                Register
                            </a>
                        @endif
                    @endauth
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="mt-4">
            {{ $slot }}
        </main>

        <!-- Login Modal -->
        <div x-show="showLoginModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showLoginModal = false"></div>
                
                <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-8 pt-8 pb-6">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold text-gray-900">Welcome Back</h3>
                            <p class="text-gray-600">Sign in to your account</p>
                        </div>
                        
                        <form method="POST" action="{{ route('login') }}" class="space-y-6">
                            @csrf
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input id="email" name="email" type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>" required>
                            </div>
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                                <input id="password" name="password" type="password" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="••••••••" required>
                            </div>
                            <div class="flex items-center justify-between">
                                <label class="flex items-center">
                                    <input type="checkbox" name="remember" class="text-blue-600 mr-2">
                                    <span class="text-sm text-gray-600">Remember me</span>
                                </label>
                                <a href="{{ route('password.request') }}" class="text-sm text-blue-600 hover:text-blue-700">Forgot password?</a>
                            </div>
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                                Sign In
                            </button>
                        </form>
                        
                        <div class="mt-6 text-center">
                            <p class="text-sm text-gray-600">
                                Don't have an account? 
                                <a href="{{ route('register') }}" class="text-blue-600 hover:text-blue-700 font-medium">Sign up</a>
                            </p>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 px-8 py-4">
                        <button @click="showLoginModal = false" class="w-full text-gray-600 hover:text-gray-700 font-medium">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Modal -->
        <div x-show="showContactModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showContactModal = false"></div>
                
                <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-8 pt-8 pb-6">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold text-gray-900">Contact Lister</h3>
                            <p class="text-gray-600">Send a message about this property</p>
                        </div>
                        
                        <form class="space-y-6">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="John">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                    <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Doe">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone (Optional)</label>
                                <input type="tel" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="+****************">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                                <textarea rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="I'm interested in this property..."></textarea>
                            </div>
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
                                Send Message
                            </button>
                        </form>
                    </div>
                    
                    <div class="bg-gray-50 px-8 py-4">
                        <button @click="showContactModal = false" class="w-full text-gray-600 hover:text-gray-700 font-medium">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
        @livewireScriptConfig
    </body>
</html>
