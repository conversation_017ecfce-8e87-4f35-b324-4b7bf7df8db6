<x-app-layout>
    <!-- Property Detail Page -->
    <div class="fade-in py-8 relative z-10" x-data="{ currentImageIndex: 0, selectedProperty: @js($property) }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <button @click="window.location.href='{{ route('properties.index') }}'" class="mb-6 text-blue-600 hover:text-blue-700 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Search
            </button>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Image Gallery -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
                        <div class="relative">
                            @if (!empty($property->images) && is_array($property->images) && count($property->images) > 0)
                                <img 
                                    x-bind:src="'{{ Storage::url('') }}' + selectedProperty.images[currentImageIndex]" 
                                    alt="{{ $property->title }}"
                                    class="w-full h-96 object-cover"
                                >
                                <div class="absolute inset-0 flex items-center justify-between p-4" x-show="selectedProperty.images.length > 1">
                                    <button @click="currentImageIndex = currentImageIndex > 0 ? currentImageIndex - 1 : selectedProperty.images.length - 1" class="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full">
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button @click="currentImageIndex = currentImageIndex < selectedProperty.images.length - 1 ? currentImageIndex + 1 : 0" class="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            @else
                                <div class="w-full h-96 bg-gray-300 flex items-center justify-center text-gray-500 rounded-lg shadow-md">No Image Available</div>
                            @endif
                        </div>
                        @if (!empty($property->images) && is_array($property->images) && count($property->images) > 1)
                            <div class="p-4">
                                <div class="flex space-x-2 overflow-x-auto">
                                    @foreach ($property->images as $index => $image)
                                        <img 
                                            src="{{ Storage::url($image) }}" 
                                            class="w-20 h-16 object-cover rounded-lg cursor-pointer opacity-60 hover:opacity-100 transition-opacity"
                                            :class="currentImageIndex === {{ $index }} ? 'opacity-100 ring-2 ring-blue-500' : ''"
                                            @click="currentImageIndex = {{ $index }}"
                                        >
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Property Details -->
                    <div class="bg-white rounded-2xl shadow-lg p-8">
                        <div class="flex justify-between items-start mb-6">
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $property->title }}</h1>
                                <p class="text-xl text-gray-600">{{ $property->city }}, {{ $property->state_region }}</p>
                            </div>
                            <span class="bg-blue-600 text-white px-4 py-2 rounded-full font-medium">{{ $property->listing_type === 'for_sale' ? 'For Sale' : 'For Rent' }}</span>
                        </div>

                        <div class="text-3xl font-bold text-blue-600 mb-6">
                            ${{ number_format($property->price) }}
                            <span class="text-lg text-gray-500">{{ $property->listing_type === 'for_rent' ? '/month' : '' }}</span>
                        </div>

                        <div class="grid grid-cols-3 gap-6 mb-8">
                            @if (isset($property->features['bedrooms']))
                                <div class="text-center p-4 bg-gray-50 rounded-lg">
                                    <i class="fas fa-bed text-2xl text-blue-600 mb-2"></i>
                                    <div class="text-2xl font-bold text-gray-900">{{ $property->features['bedrooms'] }}</div>
                                    <div class="text-sm text-gray-600">Bedrooms</div>
                                </div>
                            @endif
                            @if (isset($property->features['bathrooms']))
                                <div class="text-center p-4 bg-gray-50 rounded-lg">
                                    <i class="fas fa-bath text-2xl text-blue-600 mb-2"></i>
                                    <div class="text-2xl font-bold text-gray-900">{{ $property->features['bathrooms'] }}</div>
                                    <div class="text-sm text-gray-600">Bathrooms</div>
                                </div>
                            @endif
                            @if (isset($property->features['square_footage']))
                                <div class="text-center p-4 bg-gray-50 rounded-lg">
                                    <i class="fas fa-ruler-combined text-2xl text-blue-600 mb-2"></i>
                                    <div class="text-2xl font-bold text-gray-900">{{ $property->features['square_footage'] }}</div>
                                    <div class="text-sm text-gray-600">Sq Ft</div>
                                </div>
                            @endif
                        </div>

                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Description</h3>
                            <p class="text-gray-700 leading-relaxed">{{ $property->description }}</p>
                        </div>

                        <!-- Map Display -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Location</h3>
                            @if($property->latitude && $property->longitude)
                                <div id="propertyMap"
                                     style="height: 300px;"
                                     class="rounded-lg border border-gray-300 shadow-sm"
                                     data-latitude="{{ $property->latitude }}"
                                     data-longitude="{{ $property->longitude }}"
                                     data-title="{{ e(Str::limit($property->title, 30)) }}"></div>
                            @else
                                <div class="bg-gray-100 h-64 rounded-lg flex items-center justify-center text-gray-500">
                                    Map data not available for this property.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Contact Card -->
                    <div class="bg-white rounded-2xl shadow-lg p-6 mb-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Contact Lister</h3>
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-3">
                                <span>{{ $property->user->name[0] }}</span>
                            </div>
                            <div>
                                <div class="font-medium text-gray-900">{{ $property->user->name }}</div>
                                <div class="text-sm text-gray-600">Property Lister</div>
                            </div>
                        </div>
                        <div class="space-y-3 mb-6">
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-phone w-5"></i>
                                <span>{{ $property->user->phone }}</span>
                            </div>
                            <div class="flex items-center text-gray-600">
                                <i class="fas fa-envelope w-5"></i>
                                <span>{{ $property->user->email }}</span>
                            </div>
                        </div>
                        <button @click="$parent.showContactModal = true" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors">
                            <i class="fas fa-envelope mr-2"></i>
                            Send Message
                        </button>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-2xl shadow-lg p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-heart mr-2"></i>
                                Save Property
                            </button>
                            <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-share mr-2"></i>
                                Share Property
                            </button>
                            <button class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-lg font-medium transition-colors">
                                <i class="fas fa-calculator mr-2"></i>
                                Mortgage Calculator
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    @if($property->latitude && $property->longitude)
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const mapElement = document.getElementById('propertyMap');
            if (mapElement) {
                const lat = parseFloat(mapElement.dataset.latitude);
                const lng = parseFloat(mapElement.dataset.longitude);
                const title = mapElement.dataset.title;

                if (isNaN(lat) || isNaN(lng)) {
                    console.error('Invalid latitude or longitude for property map.');
                    mapElement.innerHTML = '<div class="p-4 text-center text-gray-500">Error: Invalid map coordinates provided.</div>';
                    return;
                }

                const map = L.map(mapElement).setView([lat, lng], 15); // Zoom level 15

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(map);

                L.marker([lat, lng]).addTo(map)
                    .bindPopup(title)
                    .openPopup();
            }
        });
    </script>
    @endif
</x-app-layout>
