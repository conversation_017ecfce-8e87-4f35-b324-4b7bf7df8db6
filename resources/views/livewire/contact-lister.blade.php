<div>
    <!-- Contact Lister Button -->
    <flux:button variant="primary" wire:click="$set('showModal', true)">
        Contact Lister
    </flux:button>

    <!-- Modal -->
    <x-modal wire:model.live="showModal">
        <form wire:submit="sendInquiry" class="p-6">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Contact {{ $property->user->name }}</h2>
            <p class="text-gray-600 mb-6">Inquiring about: <span class="font-medium">{{ $property->title }}</span></p>

            @if (session()->has('inquiry_message'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    <span class="block sm:inline">{{ session('inquiry_message') }}</span>
                </div>
            @endif

            <div class="mb-4">
                <flux:input wire:model.live="name" id="name" type="text" label="{{ __('Your Name') }}" class="mt-1 block w-full" required />
            </div>

            <div class="mb-4">
                <flux:input wire:model.live="email" id="email" type="email" label="{{ __('Your Email') }}" class="mt-1 block w-full" required />
            </div>

            <div class="mb-4">
                <flux:input wire:model.live="phone" id="phone" type="text" label="{{ __('Your Phone (Optional)') }}" class="mt-1 block w-full" />
            </div>

            <div class="mb-6">
                <flux:textarea wire:model.live="message" id="message" label="{{ __('Your Message') }}" rows="5" class="mt-1 block w-full" required></flux:textarea>
            </div>

            <div class="flex justify-end gap-4">
                <flux:button variant="outline" wire:click="$set('showModal', false)" type="button">
                    Cancel
                </flux:button>
                <flux:button variant="primary" wire:loading.attr="disabled">
                    Send Inquiry
                </flux:button>
            </div>
        </form>
    </x-modal>
</div>
