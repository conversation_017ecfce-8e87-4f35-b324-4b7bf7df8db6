<?php

use Illuminate\Auth\Events\Lockout;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Volt\Component;

new #[Layout('components.layouts.auth', ['title' => 'Sign In'])] class extends Component {
    #[Validate('required|string|email')]
    public string $email = '';

    #[Validate('required|string')]
    public string $password = '';

    public bool $remember = false;

    /**
     * Handle an incoming authentication request.
     */
    public function login(): void
    {
        $this->validate();

        $this->ensureIsNotRateLimited();

        if (! Auth::attempt(['email' => $this->email, 'password' => $this->password], $this->remember)) {
            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'email' => __('auth.failed'),
            ]);
        }

        RateLimiter::clear($this->throttleKey());
        Session::regenerate();

        $this->redirectIntended(default: route('dashboard', absolute: false), navigate: true);
    }

    /**
     * Ensure the authentication request is not rate limited.
     */
    protected function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout(request()));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => __('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the authentication rate limiting throttle key.
     */
    protected function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->email).'|'.request()->ip());
    }
}; ?>

<div class="flex flex-col">
    <x-auth-header :title="__('Welcome Back')" :description="__('Sign in to your Lokus account to continue')" />

    <!-- Session Status -->
    <x-auth-session-status class="text-center mb-6" :status="session('status')" />

    <form wire:submit="login" class="space-y-6">
        <!-- Email Address -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="email"
                :label="__('Email address')"
                type="email"
                required
                autofocus
                autocomplete="email"
                placeholder="Enter your email"
                class="w-full"
            />
        </div>

        <!-- Password -->
        <div class="space-y-2">
            <flux:input
                wire:model.live="password"
                :label="__('Password')"
                type="password"
                required
                autocomplete="current-password"
                placeholder="Enter your password"
                viewable
                class="w-full"
            />

            @if (Route::has('password.request'))
                <div class="text-right">
                    <a href="{{ route('password.request') }}" class="text-sm text-blue-600 hover:text-blue-700 transition-colors" wire:navigate>
                        Forgot your password?
                    </a>
                </div>
            @endif
        </div>

        <!-- Remember Me -->
        <div class="flex items-center">
            <flux:checkbox wire:model.live="remember" :label="__('Remember me for 30 days')" />
        </div>

        <!-- Submit Button -->
        <div class="pt-2">
            <flux:button variant="primary" type="submit" class="w-full h-12 text-base font-medium">
                <i class="fas fa-sign-in-alt mr-2"></i>
                {{ __('Sign In') }}
            </flux:button>
        </div>
    </form>

    <!-- Divider -->
    <div class="relative my-8">
        <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-200"></div>
        </div>
        <div class="relative flex justify-center text-sm">
            <span class="px-4 bg-white text-gray-500">New to Lokus?</span>
        </div>
    </div>

    <!-- Register Link -->
    @if (Route::has('register'))
        <div class="text-center">
            <a href="{{ route('register') }}" class="inline-flex items-center justify-center w-full h-12 px-4 text-base font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors" wire:navigate>
                <i class="fas fa-user-plus mr-2"></i>
                Create an Account
            </a>
        </div>
    @endif

    <!-- Demo Credentials -->
    <div class="mt-8 p-4 bg-gray-50 rounded-lg border">
        <h4 class="text-sm font-medium text-gray-900 mb-2">Demo Accounts</h4>
        <div class="text-xs text-gray-600 space-y-1">
            <div><strong>Admin:</strong> <EMAIL> / password</div>
            <div><strong>Lister:</strong> <EMAIL> / password</div>
            <div><strong>Seeker:</strong> <EMAIL> / password</div>
        </div>
    </div>
</div>
