<x-guest-layout>
    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <form method="POST" action="{{ route('login') }}">
        @csrf

        <!-- Email Address -->
        <div>
            <flux:input id="email" class="block mt-1 w-full" type="email" name="email" label="{{ __('Email') }}" :value="old('email')" required autofocus autocomplete="username" />
        </div>

        <!-- Password -->
        <div class="mt-4">
            <flux:input id="password" class="block mt-1 w-full"
                            type="password"
                            name="password"
                            label="{{ __('Password') }}"
                            required autocomplete="current-password" />
        </div>

        <!-- Remember Me -->
        <div class="block mt-4">
            <flux:checkbox id="remember_me" name="remember" :label="__('Remember me')" />
        </div>

        <div class="flex items-center justify-end mt-4">
            @if (Route::has('password.request'))
                <flux:link href="{{ route('password.request') }}" class="text-sm">
                    {{ __('Forgot your password?') }}
                </flux:link>
            @endif

            <flux:button variant="primary" type="submit" class="ms-3">
                {{ __('Log in') }}
            </flux:button>
        </div>
    </form>
</x-guest-layout>
