<x-guest-layout>
    <form method="POST" action="{{ route('register') }}">
        @csrf

        <!-- Name -->
        <div>
            <flux:input id="name" class="block mt-1 w-full" type="text" name="name" :label="__('Name')" :value="old('name')" required autofocus autocomplete="name" />
        </div>

        <!-- Email Address -->
        <div class="mt-4">
            <flux:input id="email" class="block mt-1 w-full" type="email" name="email" :label="__('Email')" :value="old('email')" required autocomplete="username" />
        </div>

        <!-- Password -->
        <div class="mt-4">
            <flux:input id="password" class="block mt-1 w-full" type="password" name="password" :label="__('Password')" required autocomplete="new-password" viewable />
        </div>

        <!-- Confirm Password -->
        <div class="mt-4">
            <flux:input id="password_confirmation" class="block mt-1 w-full" type="password" name="password_confirmation" :label="__('Confirm Password')" required autocomplete="new-password" viewable />
        </div>

        <!-- Role Selection -->
        <div class="mt-4">
            <flux:select id="role" name="role" :label="__('I want to...')" required>
                <option value="">{{ __('Select an option') }}</option>
                <option value="seeker" {{ old('role') == 'seeker' ? 'selected' : '' }}>{{ __('Just look for properties') }}</option>
                <option value="lister" {{ old('role') == 'lister' ? 'selected' : '' }}>{{ __('List properties') }}</option>
            </flux:select>
        </div>

        <div class="flex items-center justify-end mt-4">
            <flux:link href="{{ route('login') }}" class="text-sm">
                {{ __('Already registered?') }}
            </flux:link>

            <flux:button variant="primary" class="ms-4" type="submit">
                {{ __('Register') }}
            </flux:button>
        </div>
    </form>
</x-guest-layout>
