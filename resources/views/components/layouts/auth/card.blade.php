<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />

        <title>{{ $title ?? config('app.name') }} - Lokus</title>

        <link rel="icon" href="/favicon.ico" sizes="any">
        <link rel="icon" href="/favicon.svg" type="image/svg+xml">
        <link rel="apple-touch-icon" href="/apple-touch-icon.png">

        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

        <!-- FontAwesome Icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

        @vite(['resources/css/app.css', 'resources/js/app.js'])
        @fluxAppearance
    </head>
    <body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 antialiased">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-white/20 bg-[radial-gradient(circle_at_1px_1px,rgba(59,130,246,0.15)_1px,transparent_0)] [background-size:20px_20px]"></div>

        <div class="relative flex min-h-screen flex-col items-center justify-center gap-8 p-6 md:p-10">
            <div class="flex w-full max-w-md flex-col gap-8">
                <!-- Logo Section -->
                <div class="text-center">
                    <a href="{{ route('home') }}" class="inline-flex flex-col items-center gap-3 font-medium group" wire:navigate>
                        <!-- Logo Icon -->
                        <div class="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-600 to-blue-700 shadow-lg group-hover:shadow-xl transition-all duration-200 group-hover:scale-105">
                            <i class="fas fa-home text-2xl text-white"></i>
                        </div>

                        <!-- Brand Name -->
                        <div class="flex flex-col items-center">
                            <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                                Lokus
                            </h1>
                            <p class="text-sm text-gray-600 mt-1">Find Your Perfect Home</p>
                        </div>
                    </a>
                </div>

                <!-- Auth Card -->
                <div class="flex flex-col gap-6">
                    <div class="rounded-2xl border border-white/20 bg-white/80 backdrop-blur-sm text-gray-800 shadow-xl">
                        <div class="px-8 py-10 md:px-10 md:py-12">
                            {{ $slot }}
                        </div>
                    </div>

                    <!-- Footer Links -->
                    <div class="text-center">
                        <div class="flex items-center justify-center space-x-6 text-sm text-gray-600">
                            <a href="{{ route('home') }}" class="hover:text-blue-600 transition-colors" wire:navigate>
                                <i class="fas fa-home mr-1"></i>
                                Home
                            </a>
                            <a href="mailto:<EMAIL>" class="hover:text-blue-600 transition-colors">
                                <i class="fas fa-envelope mr-1"></i>
                                Support
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @fluxScripts
    </body>
</html>
